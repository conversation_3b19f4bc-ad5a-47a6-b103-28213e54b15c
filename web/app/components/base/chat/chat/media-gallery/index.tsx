import type { FC } from 'react'
import {
  memo,
  useEffect,
  useState,
} from 'react'
import type { ChatItem } from '../../types'

import 'photoswipe/dist/photoswipe.css'
import { Gallery, Item } from 'react-photoswipe-gallery'

import 'swiper/css'
import 'swiper/css/pagination'
import { Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'
import { useTranslation } from 'react-i18next'

type MediaGalleryProps = {
  chatList: ChatItem[]
}

type ImageWithDimensions = {
  url: string
  width: number
  height: number
}

const preloadImage = (url: string): Promise<ImageWithDimensions> =>
  new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        url,
        width: img.naturalWidth,
        height: img.naturalHeight,
      })
    }
    img.onerror = () => {
      resolve({
        url,
        width: 800, // fallback width
        height: 600, // fallback height
      })
    }
    img.src = url
  })

const MediaGallery: FC<MediaGalleryProps> = () => {
  const { t } = useTranslation()

  const imageUrls = [
    'https://cdn.photoswipe.com/photoswipe-demo-images/photos/1/img-2500.jpg',
    'https://cdn.photoswipe.com/photoswipe-demo-images/photos/2/img-2500.jpg',
  ]

  const [images, setImages] = useState<ImageWithDimensions[]>([])

  useEffect(() => {
    const fetchDimensions = async () => {
      const data = await Promise.all(imageUrls.map(preloadImage))
      setImages(data)
    }
    fetchDimensions()
  }, [])

  return (
    <>
      <div className="w-full truncate text-2xl font-semibold text-[#565656]">
        {t('common.chat.mediaGallery')}
      </div>
      <div className="mb-4 w-full truncate text-lg font-medium text-[#565656]">
        {t('common.chat.viewFullScreen')}
      </div>
      <Gallery options={{ trapFocus: false }}>
        <Swiper
          pagination={{
            el: '.swiper-custom-pagination',
          }}
          modules={[Pagination]}
          spaceBetween={20}
          slidesPerView={images.length > 1 ? 1.25 : 1}
          className="h-[calc(100%-110px)] rounded-bl-2xl rounded-tl-2xl"
        >
          {images.map((img, index) => (
            <SwiperSlide key={`${img.url}-${index}`}>
              <div className="relative h-full">
                <Item
                  original={img.url}
                  thumbnail={img.url}
                  width={img.width}
                  height={img.height}
                >
                  {({ ref, open }) => (
                    <img
                      src={img.url}
                      ref={ref as React.Ref<HTMLImageElement>}
                      onClick={open}
                      className="size-full rounded-2xl object-fill cursor-pointer"
                      alt=""
                    />
                  )}
                </Item>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        <div className="swiper-custom-pagination mx-auto mt-2 flex justify-center" />
      </Gallery>
    </>
  )
}

export default memo(MediaGallery)
