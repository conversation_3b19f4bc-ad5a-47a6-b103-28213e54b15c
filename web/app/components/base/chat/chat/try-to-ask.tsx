import type { FC } from 'react'
import { memo } from 'react'
import type { OnSend } from '../types'
import Button from '@/app/components/base/button'
import cn from '@/utils/classnames'
import { RiSendPlane2Fill } from '@remixicon/react'
import { useTranslation } from 'react-i18next'

type TryToAskProps = {
  suggestedQuestions: string[]
  onSend: OnSend
  isMobile?: boolean
}
const TryToAsk: FC<TryToAskProps> = ({
  suggestedQuestions,
  onSend,
  isMobile,
}) => {
  const { t } = useTranslation()

  return (
    <div className='mb-2 w-full py-2'>
      <div className={cn('follow-up-question-list flex flex-wrap', isMobile && 'justify-end')}>
        <div className='w-full truncate text-center text-2xl font-semibold text-[#565656]'>{t('common.chat.followupQuestions')}</div>
        {
          suggestedQuestions.map((suggestQuestion, index) => (
            <Button
              key={index}
              variant='secondary-accent'
              className='mb-1 mr-1 justify-between gap-2 last:mr-0'
              onClick={() => onSend(suggestQuestion)}
            >
              {suggestQuestion}
              <span>
                <RiSendPlane2Fill className='size-8' />
              </span>
            </Button>
          ))
        }
      </div>
    </div>
  )
}

export default memo(TryToAsk)
