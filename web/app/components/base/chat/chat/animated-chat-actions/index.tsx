'use client'

import type { FC } from 'react'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, usePathname } from 'next/navigation'
import Recorder from 'js-audio-recorder'
import { RiLoader2Line } from '@remixicon/react'
import cn from '@/utils/classnames'
import { audioToText } from '@/service/share'
import { StopCircle } from '@/app/components/base/icons/src/vender/solid/mediaAndDevices'
import { convertToMp3 } from '@/app/components/base/voice-input/utils'
import Button from '../../../button'
import type { OnSend } from '../../types'
import s from './style.module.css'

type AnimationState = 'recording' | 'generating' | 'idle'

type AnimatedChatActionsProps = {
  isResponding?: boolean
  noStopResponding?: boolean
  onStopResponding?: () => void
  onSend?: OnSend
}

const IMAGE_SOURCES = {
  idle: '/3D-character-GUI/micro.png',
  recording: '/3D-character-GUI/listen.png',
  thinking: '/3D-character-GUI/thinking.png',
  answer: '/3D-character-GUI/answer.png',
} as const

const AnimatedChatActions: FC<AnimatedChatActionsProps> = ({
  isResponding,
  noStopResponding,
  onStopResponding,
  onSend,
}) => {
  const { t } = useTranslation()
  const pathname = usePathname()
  const params = useParams()

  const [chatState, setChatState] = useState<AnimationState>('idle')
  const [isRecording, setIsRecording] = useState(false)
  const [isConverting, setIsConverting] = useState(false)

  const recorder = useRef<any>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize recorder lazily to prevent unnecessary AudioContext creation
  const initializeRecorder = useCallback(() => {
    if (!recorder.current) {
      try {
        recorder.current = new Recorder({
          sampleBits: 16,
          sampleRate: 16000,
          numChannels: 1,
          compiling: false,
        })
      }
      catch (error) {
        console.warn('Failed to initialize recorder:', error)
      }
    }
    return recorder.current
  }, [])

  useEffect(() => {
    return () => {
      try {
        if (recorder.current) {
          recorder.current.stop()
          if (typeof recorder.current.destroy === 'function')
            recorder.current.destroy()
          recorder.current = null
        }
      }
      catch (err) {
        console.warn('Error cleaning up recorder:', err)
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    if (isResponding && imgRef.current) {
      imgRef.current.src = IMAGE_SOURCES.thinking
      timerRef.current = setTimeout(() => {
        if (imgRef.current)
          imgRef.current.src = IMAGE_SOURCES.answer
      }, 2000)
    }
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }
    }
  }, [isResponding])

  const handleStopRecorder = useCallback(async () => {
    if (!isRecording || !recorder.current) return
    setIsRecording(false)
    setIsConverting(true)

    try {
      recorder.current.stop()
      const mp3Blob = convertToMp3(recorder.current)
      const mp3File = new File([mp3Blob], 'temp.mp3', { type: 'audio/mp3' })
      const formData = new FormData()
      formData.append('file', mp3File)
      formData.append('word_timestamps', 'disabled')

      let url = ''
      let isPublic = false

      if (params.token) {
        url = '/audio-to-text'
        isPublic = true
      }
      else if (params.appId) {
        if (pathname.search('explore/installed') > -1)
          url = `/installed-apps/${params.appId}/audio-to-text`
        else
          url = `/apps/${params.appId}/audio-to-text`
      }

      try {
        const audioResponse = await audioToText(url, isPublic, formData)
        onSend?.(audioResponse.text)
        setChatState('idle')
      }
      catch {
        onSend?.('')
        setChatState('idle')
      }
    }
    catch (error) {
      console.warn('Error in handleStopRecorder:', error)
      setChatState('idle')
    }
    finally {
      setIsConverting(false)
    }
  }, [isRecording, onSend, params.appId, params.token, pathname])

  const handleStartRecord = useCallback(async () => {
    try {
      if (isRecording) return

      const recorderInstance = initializeRecorder()
      if (!recorderInstance) {
        setChatState('idle')
        return
      }

      await recorderInstance.start()
      setIsRecording(true)
      setIsConverting(false)
      setChatState('recording')
    }
    catch {
      setChatState('idle')
    }
  }, [isRecording, initializeRecorder])

  const content = useMemo(() => {
    if (isResponding) {
      return (
        <>
          <div className="relative flex items-center justify-center">
            <button className="z-10">
              <img ref={imgRef} className="w-[360px]" src={IMAGE_SOURCES.thinking} />
            </button>
            <div className={cn(s['sound-waves'], 'absolute inset-0')}>
              <div className={cn(s.wave, s['wave-1'])} />
              <div className={cn(s.wave, s['wave-2'])} />
            </div>
          </div>
          {!noStopResponding && (
            <div className="stop-respond-action z-20 mb-2 flex justify-center">
              <Button onClick={onStopResponding}>
                <StopCircle className="mr-2 size-10 text-red-500" />
                <span className="text-xl font-normal text-red-500">{t('appDebug.operation.stopResponding')}</span>
              </Button>
            </div>
          )}
        </>
      )
    }

    if (chatState === 'recording') {
      return (
        <>
          <div className="relative flex items-center justify-center">
            <button className="z-10">
              <img className="w-[360px]" src={IMAGE_SOURCES.recording} />
            </button>
            <div className={cn(s['sound-waves'], 'absolute inset-0')}>
              <div className={cn(s.wave, s['wave-1'])} />
              <div className={cn(s.wave, s['wave-2'])} />
            </div>
          </div>
          <div className="z-20">
            {isRecording && (
              <div
                className="mx-auto flex size-24 cursor-pointer items-center justify-center rounded-lg"
                onClick={handleStopRecorder}
              >
                <StopCircle className="size-full text-red-600" />
              </div>
            )}
            {isConverting && (
              <div className="mx-auto flex size-24 cursor-pointer items-center justify-center rounded-lg">
                <RiLoader2Line className="size-full animate-spin text-primary-700" />
              </div>
            )}
          </div>
        </>
      )
    }

    if (chatState === 'idle') {
      return (
        <div className="relative flex items-center justify-center">
          <button className="z-10" onClick={handleStartRecord}>
            <img className="w-[360px]" src={IMAGE_SOURCES.idle} />
          </button>
          <div className={cn(s['sound-waves'], 'absolute inset-0')}>
            <div className={cn(s.wave, s['wave-1'])} />
            <div className={cn(s.wave, s['wave-2'])} />
          </div>
        </div>
      )
    }

    return null
  }, [isResponding, chatState, isRecording, isConverting, noStopResponding, onStopResponding, handleStopRecorder, handleStartRecord, t])

  return (
    <div>
      {content}
    </div>
  )
}

export default memo(AnimatedChatActions)
