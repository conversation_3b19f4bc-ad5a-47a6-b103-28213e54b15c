import { useCallback, useState } from 'react'

import {
  RiChatAiLine,
  RiCustomerService2Line,
  // RiResetLeftLine,
} from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import {
  useChatWithHistoryContext,
} from '../context'
// import ViewFormDropdown from '@/app/components/base/chat/chat-with-history/inputs-form/view-form-dropdown'
import Confirm from '@/app/components/base/confirm'
import RenameModal from '@/app/components/base/chat/chat-with-history/sidebar/rename-modal'
import { SimpleSelect } from '@/app/components/base/select'
import type { ConversationItem } from '@/models/share'
import type { Item } from '@/app/components/base/select'
import { languages } from '@/i18n/language'
import Button from '../../../button'
import { changeLanguage } from '@/i18n/i18next-config'

const Header = () => {
  const {
    appData,
    conversationRenaming,
    handleRenameConversation,
    handleDeleteConversation,
    handleNewConversation,
  } = useChatWithHistoryContext()
  const { t } = useTranslation()

  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)
  const [showRename, setShowRename] = useState<ConversationItem | null>(null)

  const handleCancelConfirm = useCallback(() => {
    setShowConfirm(null)
  }, [])
  const handleDelete = useCallback(() => {
    if (showConfirm)
      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })
  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])
  const handleCancelRename = useCallback(() => {
    setShowRename(null)
  }, [])
  const handleRename = useCallback((newName: string) => {
    if (showRename)
      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })
  }, [showRename, handleRenameConversation, handleCancelRename])

  const handleSelectLanguage = async (item: Item) => {
    changeLanguage(item.value.toString())
  }

  return (
    <>
      <div className='absolute right-10 top-5 z-10 flex shrink-0 items-center justify-end p-3'>
        <div className='flex items-center gap-1'>
          <Button className='call-assistant-action mr-4 border-none text-2xl shadow-none' variant='secondary' onClick={handleNewConversation}>
            <RiChatAiLine className='mr-3 size-10 text-text-accent-light-mode-only' />
            <span className="truncate">{t('common.chat.newConversation')}</span>
          </Button>

          <Button className='call-assistant-action mr-4 border-none text-2xl shadow-none' variant='secondary'>
            <RiCustomerService2Line className='mr-3 size-10 text-text-accent-light-mode-only' />
            <span className="truncate">{t('common.chat.makeACall')}</span>
          </Button>

          <SimpleSelect
            defaultValue={appData?.site.default_language}
            items={languages.filter(item => item.supported)}
            onSelect={item => handleSelectLanguage(item)}
          />
        </div>
      </div>
      {!!showConfirm && (
        <Confirm
          title={t('share.chat.deleteConversation.title')}
          content={t('share.chat.deleteConversation.content') || ''}
          isShow
          onCancel={handleCancelConfirm}
          onConfirm={handleDelete}
        />
      )}
      {showRename && (
        <RenameModal
          isShow
          onClose={handleCancelRename}
          saveLoading={conversationRenaming}
          name={showRename?.name || ''}
          onSave={handleRename}
        />
      )}
    </>
  )
}

export default Header
