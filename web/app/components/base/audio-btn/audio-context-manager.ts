/**
 * Global AudioContext Manager to prevent AudioContext resource leaks
 * and limit the number of concurrent AudioContext instances.
 */

class AudioContextManager {
  private static instance: AudioContextManager
  private audioContexts: Set<AudioContext> = new Set()
  private maxContexts = 6 // Browser limit is usually around 6-8

  public static getInstance(): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager()
    }
    return AudioContextManager.instance
  }

  public createAudioContext(): AudioContext | null {
    try {
      // Clean up closed contexts first
      this.cleanupClosedContexts()

      // Check if we're at the limit
      if (this.audioContexts.size >= this.maxContexts) {
        console.warn(`AudioContext limit reached (${this.maxContexts}). Cleaning up oldest contexts.`)
        this.cleanupOldestContext()
      }

      const audioContext = new AudioContext()
      this.audioContexts.add(audioContext)

      // Add event listener to remove from set when closed
      audioContext.addEventListener('statechange', () => {
        if (audioContext.state === 'closed') {
          this.audioContexts.delete(audioContext)
        }
      })

      return audioContext
    }
    catch (error) {
      console.error('Failed to create AudioContext:', error)
      return null
    }
  }

  public closeAudioContext(audioContext: AudioContext): void {
    try {
      if (audioContext.state !== 'closed') {
        audioContext.close()
      }
      this.audioContexts.delete(audioContext)
    }
    catch (error) {
      console.warn('Error closing AudioContext:', error)
    }
  }

  private cleanupClosedContexts(): void {
    for (const context of this.audioContexts) {
      if (context.state === 'closed') {
        this.audioContexts.delete(context)
      }
    }
  }

  private cleanupOldestContext(): void {
    // Close the first (oldest) context in the set
    const firstContext = this.audioContexts.values().next().value
    if (firstContext) {
      this.closeAudioContext(firstContext)
    }
  }

  public getActiveContextCount(): number {
    this.cleanupClosedContexts()
    return this.audioContexts.size
  }

  public closeAllContexts(): void {
    for (const context of this.audioContexts) {
      try {
        if (context.state !== 'closed') {
          context.close()
        }
      }
      catch (error) {
        console.warn('Error closing AudioContext during cleanup:', error)
      }
    }
    this.audioContexts.clear()
  }

  public static disposeInstance(): void {
    if (AudioContextManager.instance) {
      AudioContextManager.instance.closeAllContexts()
      AudioContextManager.instance = null as any
    }
  }
}

export default AudioContextManager
